-- Add DELETE policy for ai_generated_images table
-- This allows users to delete AI-generated images based on their role and organization membership

CREATE POLICY "Users can delete their own AI-generated images"
ON public.ai_generated_images FOR DELETE
USING (
  -- User who created the image can delete it
  user_id = auth.uid()
  OR
  -- Platform admins can delete any images
  EXISTS (
    SELECT 1
    FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
  OR
  -- Brand admins can delete images in their organization
  EXISTS (
    SELECT 1
    FROM public.organization_memberships om
    JOIN public.users u ON u.id = om.user_id
    WHERE om.organization_id = ai_generated_images.organization_id
    AND om.user_id = auth.uid()
    AND u.role = 'brand_admin'
  )
);

-- Add storage policy for deleting files from ai-generated bucket
-- This ensures users can delete the actual files from storage
INSERT INTO storage.policies (bucket_id, name, definition, operation)
VALUES (
  'ai-generated',
  'Users can delete their AI-generated files',
  jsonb_build_object(
    'or', jsonb_build_array(
      -- User who created the file can delete it
      jsonb_build_object(
        'resource', jsonb_build_object(
          'object', jsonb_build_object(
            'user_id', 'uid()'
          )
        )
      ),
      -- Platform admins can delete any files
      jsonb_build_object(
        'exists', jsonb_build_object(
          'from', 'public.users',
          'where', jsonb_build_object(
            'and', jsonb_build_array(
              jsonb_build_object('id', 'uid()'),
              jsonb_build_object(
                'in', jsonb_build_array(
                  'role',
                  jsonb_build_array('platform_super', 'platform_admin')
                )
              )
            )
          )
        )
      )
    )
  ),
  'DELETE'
)
ON CONFLICT (bucket_id, name, operation) DO UPDATE
SET definition = EXCLUDED.definition;